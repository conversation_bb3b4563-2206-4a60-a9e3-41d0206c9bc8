<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM API Cost Calculator - CPR Store Review Automation</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .calculator-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .results-section {
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .text-simulator {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
            grid-column: 1 / -1;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            font-family: inherit;
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            margin-top: 10px;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .result-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .result-card h3 {
            font-size: 0.9em;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .result-card .value {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .provider-comparison {
            margin-top: 25px;
        }
        
        .provider-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .provider-table th {
            background: #2c3e50;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .provider-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .provider-table tr:hover {
            background: #f8f9fa;
        }
        
        .cost-low { color: #27ae60; font-weight: bold; }
        .cost-medium { color: #f39c12; font-weight: bold; }
        .cost-high { color: #e74c3c; font-weight: bold; }
        
        .chart-container {
            margin-top: 25px;
            height: 400px;
            position: relative;
        }
        
        .optimization-tips {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 20px;
            margin-top: 25px;
            border-radius: 0 8px 8px 0;
        }
        
        .optimization-tips h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }
        
        .optimization-tips ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .optimization-tips li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .optimization-tips li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }
        
        .text-simulator h3 {
            color: #856404;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .simulator-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .simulator-results {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .token-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .token-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .token-card h4 {
            color: #495057;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .token-card .token-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .simulator-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LLM API Cost Calculator</h1>
            <p>CPR Store Review Response Automation - Complete Cost Analysis Tool</p>
        </div>
        
        <div class="main-content">
            <div class="calculator-section">
                <h2>Configuration Parameters</h2>
                
                <div class="form-group">
                    <label for="numStores">Number of CPR Stores</label>
                    <input type="number" id="numStores" value="100" min="1" max="1000">
                </div>
                
                <div class="form-group">
                    <label for="responsesPerDay">Responses per Store per Day</label>
                    <input type="number" id="responsesPerDay" value="30" min="1" max="100">
                </div>
                
                <div class="form-group">
                    <label for="inputTokens">Input Tokens per Response</label>
                    <input type="number" id="inputTokens" value="45" min="10" max="200">
                </div>
                
                <div class="form-group">
                    <label for="outputTokens">Output Tokens per Response</label>
                    <input type="number" id="outputTokens" value="68" min="20" max="150">
                </div>
                
                <div class="form-group">
                    <label for="primaryProvider">Primary LLM Provider</label>
                    <select id="primaryProvider">
                        <option value="groq-llama-3.1-8b">Groq Llama 3.1 8B (Most Economic)</option>
                        <option value="mistral-nemo">Mistral Nemo</option>
                        <option value="deepseek-v3">DeepSeek V3 (Standard)</option>
                        <option value="deepseek-v3-offpeak">DeepSeek V3 (Off-Peak 50% Off)</option>
                        <option value="gemini-2.0-flash-lite">Google Gemini 2.0 Flash Lite</option>
                        <option value="openai-gpt-4.1-nano">OpenAI GPT-4.1 Nano</option>
                        <option value="claude-haiku-3">Anthropic Claude Haiku 3</option>
                        <option value="gemini-2.0-flash">Google Gemini 2.0 Flash</option>
                        <option value="openai-gpt-4.1-mini">OpenAI GPT-4.1 Mini</option>
                        <option value="claude-haiku-3.5">Anthropic Claude Haiku 3.5</option>
                        <option value="qwen-2.5-max">Alibaba Qwen 2.5 Max</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="batchProcessing">Enable Batch Processing (where available)</label>
                    <select id="batchProcessing">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
                
                <button class="btn" onclick="calculateCosts()">Calculate Costs</button>
            </div>
            
            <div class="results-section">
                <h2>Cost Analysis Results</h2>
                
                <div class="results-grid" id="resultsGrid">
                    <div class="result-card">
                        <h3>Cost per Response</h3>
                        <div class="value" id="costPerResponse">$0.000</div>
                    </div>
                    <div class="result-card">
                        <h3>Daily Cost</h3>
                        <div class="value" id="dailyCost">$0.00</div>
                    </div>
                    <div class="result-card">
                        <h3>Monthly Cost</h3>
                        <div class="value" id="monthlyCost">$0.00</div>
                    </div>
                    <div class="result-card">
                        <h3>Annual Cost</h3>
                        <div class="value" id="annualCost">$0.00</div>
                    </div>
                </div>
                
                <div class="provider-comparison">
                    <h3>Provider Comparison (Ranked by Cost - Current Configuration)</h3>
                    <table class="provider-table" id="providerTable">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Provider</th>
                                <th>Model</th>
                                <th>Cost/Response</th>
                                <th>Monthly Cost</th>
                                <th>Rate Limit (RPM)</th>
                            </tr>
                        </thead>
                        <tbody id="providerTableBody">
                        </tbody>
                    </table>
                </div>
                
                <div class="chart-container">
                    <canvas id="costChart"></canvas>
                </div>
                
                <div class="optimization-tips">
                    <h3>Cost Optimization Recommendations</h3>
                    <ul id="optimizationTips">
                    </ul>
                </div>
            </div>
            
            <div class="text-simulator">
                <h3>🧮 Text Input/Output Cost Simulator</h3>
                <p style="text-align: center; margin-bottom: 20px; color: #856404;">
                    Test real input/output scenarios to see exact token counts and costs
                </p>
                
                <div class="simulator-grid">
                    <div class="form-group">
                        <label for="inputText">Input Text (Review + System Prompt)</label>
                        <textarea id="inputText" placeholder="Example: You are a customer service AI for CPR cell phone repair. Please respond to this customer review: 'Great service! Fixed my iPhone screen quickly and professionally. Highly recommend!'">You are a customer service AI for CPR cell phone repair. Please respond to this customer review: "Great service! Fixed my iPhone screen quickly and professionally. Highly recommend!"</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="outputText">Expected Output Text (Generated Response)</label>
                        <textarea id="outputText" placeholder="Example response with CPR keywords...">Thank you so much for your wonderful review! We're thrilled to hear about your positive experience with our iPhone screen repair service. At CPR, we pride ourselves on providing quick, professional phone screen repairs, battery replacements, and comprehensive device solutions. Your recommendation means the world to us! We also offer game console repairs, laptop screen repairs, and tablet fixes. Visit us again for any future phone repair needs!</textarea>
                    </div>
                </div>
                
                <button class="btn btn-secondary" onclick="simulateTextCost()">Calculate Text-Based Cost</button>
                
                <div class="simulator-results" id="simulatorResults" style="display: none;">
                    <div class="token-info">
                        <div class="token-card">
                            <h4>Input Tokens</h4>
                            <div class="token-value" id="simInputTokens">0</div>
                        </div>
                        <div class="token-card">
                            <h4>Output Tokens</h4>
                            <div class="token-value" id="simOutputTokens">0</div>
                        </div>
                        <div class="token-card">
                            <h4>Total Tokens</h4>
                            <div class="token-value" id="simTotalTokens">0</div>
                        </div>
                        <div class="token-card">
                            <h4>Cost per Response</h4>
                            <div class="token-value" id="simCostPerResponse">$0.000</div>
                        </div>
                    </div>
                    
                    <h4 style="margin-top: 20px; margin-bottom: 15px;">Cost Breakdown by Provider:</h4>
                    <table class="provider-table" id="simulatorTable">
                        <thead>
                            <tr>
                                <th>Provider</th>
                                <th>Model</th>
                                <th>Cost per Response</th>
                                <th>Daily Cost (100 stores, 30 resp/day)</th>
                            </tr>
                        </thead>
                        <tbody id="simulatorTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // LLM Provider Data (Ranked from most economic to most expensive)
        const llmProviders = {
            'groq-llama-3.1-8b': {
                name: 'Groq',
                model: 'Llama 3.1 8B',
                inputPrice: 0.05,
                outputPrice: 0.08,
                rateLimit: 30000,
                batchDiscount: 0.25
            },
            'mistral-nemo': {
                name: 'Mistral AI',
                model: 'Nemo',
                inputPrice: 0.15,
                outputPrice: 0.15,
                rateLimit: 1000,
                batchDiscount: 0.50
            },
            'deepseek-v3': {
                name: 'DeepSeek',
                model: 'V3 (Standard)',
                inputPrice: 0.27,
                outputPrice: 1.10,
                rateLimit: 5000,
                batchDiscount: 0
            },
            'deepseek-v3-offpeak': {
                name: 'DeepSeek',
                model: 'V3 (Off-Peak 50% Off)',
                inputPrice: 0.135,
                outputPrice: 0.55,
                rateLimit: 5000,
                batchDiscount: 0
            },
            'gemini-2.0-flash-lite': {
                name: 'Google',
                model: 'Gemini 2.0 Flash Lite',
                inputPrice: 0.075,
                outputPrice: 0.30,
                rateLimit: 60,
                batchDiscount: 0
            },
            'openai-gpt-4.1-nano': {
                name: 'OpenAI',
                model: 'GPT-4.1 Nano',
                inputPrice: 0.10,
                outputPrice: 0.40,
                rateLimit: 30000,
                batchDiscount: 0.50
            },
            'claude-haiku-3': {
                name: 'Anthropic',
                model: 'Claude Haiku 3',
                inputPrice: 0.25,
                outputPrice: 1.25,
                rateLimit: 1000,
                batchDiscount: 0.50
            },
            'gemini-2.0-flash': {
                name: 'Google',
                model: 'Gemini 2.0 Flash',
                inputPrice: 0.15,
                outputPrice: 0.60,
                rateLimit: 60,
                batchDiscount: 0
            },
            'openai-gpt-4.1-mini': {
                name: 'OpenAI',
                model: 'GPT-4.1 Mini',
                inputPrice: 0.40,
                outputPrice: 1.60,
                rateLimit: 10000,
                batchDiscount: 0.50
            },
            'claude-haiku-3.5': {
                name: 'Anthropic',
                model: 'Claude Haiku 3.5',
                inputPrice: 0.80,
                outputPrice: 4.00,
                rateLimit: 1000,
                batchDiscount: 0.50
            },
            'qwen-2.5-max': {
                name: 'Alibaba',
                model: 'Qwen 2.5 Max',
                inputPrice: 1.60,
                outputPrice: 6.40,
                rateLimit: 5000,
                batchDiscount: 0
            }
        };

        let costChart = null;

        // Simple token estimation function (approximation)
        function estimateTokens(text) {
            // Rough approximation: 1 token ≈ 4 characters for English text
            // This is a simplified estimation - actual tokenization varies by model
            return Math.ceil(text.length / 4);
        }

        function calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing) {
            const inputCost = (inputTokens * provider.inputPrice) / 1000000;
            const outputCost = (outputTokens * provider.outputPrice) / 1000000;
            let totalCost = inputCost + outputCost;
            
            if (batchProcessing && provider.batchDiscount > 0) {
                totalCost = totalCost * (1 - provider.batchDiscount);
            }
            
            return totalCost;
        }

        function formatCurrency(amount) {
            if (amount < 0.01) {
                return `$${amount.toFixed(6)}`;
            }
            return `$${amount.toFixed(2)}`;
        }

        function getCostClass(cost) {
            if (cost < 0.00005) return 'cost-low';
            if (cost < 0.0002) return 'cost-medium';
            return 'cost-high';
        }

        function calculateCosts() {
            const numStores = parseInt(document.getElementById('numStores').value);
            const responsesPerDay = parseInt(document.getElementById('responsesPerDay').value);
            const inputTokens = parseInt(document.getElementById('inputTokens').value);
            const outputTokens = parseInt(document.getElementById('outputTokens').value);
            const primaryProviderId = document.getElementById('primaryProvider').value;
            const batchProcessing = document.getElementById('batchProcessing').value === 'true';

            const primaryProvider = llmProviders[primaryProviderId];
            const costPerResponse = calculateCostPerResponse(primaryProvider, inputTokens, outputTokens, batchProcessing);
            const dailyCost = costPerResponse * numStores * responsesPerDay;
            const monthlyCost = dailyCost * 30;
            const annualCost = dailyCost * 365;

            // Update main results
            document.getElementById('costPerResponse').textContent = formatCurrency(costPerResponse);
            document.getElementById('dailyCost').textContent = formatCurrency(dailyCost);
            document.getElementById('monthlyCost').textContent = formatCurrency(monthlyCost);
            document.getElementById('annualCost').textContent = formatCurrency(annualCost);

            // Update provider comparison table
            updateProviderTable(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing);

            // Update chart
            updateCostChart(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing);

            // Update optimization tips
            updateOptimizationTips(numStores, responsesPerDay, costPerResponse, primaryProvider);
        }

        function updateProviderTable(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing) {
            const tbody = document.getElementById('providerTableBody');
            tbody.innerHTML = '';

            const providerCosts = [];

            Object.entries(llmProviders).forEach(([id, provider]) => {
                const costPerResponse = calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing);
                const monthlyCost = costPerResponse * numStores * responsesPerDay * 30;
                
                providerCosts.push({
                    id,
                    provider,
                    costPerResponse,
                    monthlyCost
                });
            });

            // Sort by cost per response (already ordered in the object, but ensure sorting)
            providerCosts.sort((a, b) => a.costPerResponse - b.costPerResponse);

            providerCosts.forEach(({ provider, costPerResponse, monthlyCost }, index) => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td><strong>#${index + 1}</strong></td>
                    <td>${provider.name}</td>
                    <td>${provider.model}</td>
                    <td class="${getCostClass(costPerResponse)}">${formatCurrency(costPerResponse)}</td>
                    <td class="${getCostClass(monthlyCost)}">${formatCurrency(monthlyCost)}</td>
                    <td>${provider.rateLimit.toLocaleString()}</td>
                `;
            });
        }

        function updateCostChart(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing) {
            const ctx = document.getElementById('costChart').getContext('2d');
            
            if (costChart) {
                costChart.destroy();
            }

            const storeScales = [10, 25, 50, 100, 200, 300];
            const datasets = [];

            // Get top 6 most cost-effective providers
            const providerCosts = Object.entries(llmProviders).map(([id, provider]) => ({
                id,
                provider,
                costPerResponse: calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing)
            })).sort((a, b) => a.costPerResponse - b.costPerResponse).slice(0, 6);

            const colors = ['#27ae60', '#f39c12', '#3498db', '#e74c3c', '#9b59b6', '#1abc9c'];

            providerCosts.forEach((item, index) => {
                const data = storeScales.map(stores => {
                    return item.costPerResponse * stores * responsesPerDay * 30;
                });

                datasets.push({
                    label: `${item.provider.name} ${item.provider.model}`,
                    data: data,
                    borderColor: colors[index],
                    backgroundColor: colors[index] + '20',
                    tension: 0.4
                });
            });

            costChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: storeScales.map(s => `${s} Stores`),
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Monthly Cost Scaling by Number of Stores (Top 6 Most Economic)'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Monthly Cost ($)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Number of Stores'
                            }
                        }
                    }
                }
            });
        }

        function updateOptimizationTips(numStores, responsesPerDay, costPerResponse, provider) {
            const tips = document.getElementById('optimizationTips');
            tips.innerHTML = '';

            const totalDailyRequests = numStores * responsesPerDay;
            const maxDailyRequests = provider.rateLimit * 60 * 24;

            const optimizations = [];

            // Rate limit warnings
            if (totalDailyRequests > maxDailyRequests * 0.8) {
                optimizations.push(`⚠️ Approaching rate limit! Current: ${totalDailyRequests.toLocaleString()}, Max: ${maxDailyRequests.toLocaleString()}`);
            }

            // Cost optimization suggestions
            if (costPerResponse > 0.00002) {
                optimizations.push('💡 Consider Groq Llama 3.1 8B for ultra-low costs ($0.000008/response)');
            }

            if (provider.name === 'DeepSeek' && !provider.model.includes('Off-Peak')) {
                optimizations.push('🕐 Use DeepSeek off-peak hours (UTC 16:30-00:30) for 50% savings');
            }

            if (provider.batchDiscount > 0 && document.getElementById('batchProcessing').value === 'false') {
                optimizations.push(`📦 Enable batch processing for ${(provider.batchDiscount * 100)}% cost savings`);
            }

            // Token optimization
            optimizations.push('✂️ Optimize prompts to reduce input tokens by 20-40%');
            optimizations.push('🎯 Set max_tokens parameter to limit output length');
            optimizations.push('📝 Use template-based responses for consistency');

            // Scaling recommendations
            if (numStores > 100) {
                optimizations.push('🔄 Consider multi-provider strategy for redundancy');
                optimizations.push('⚡ Implement intelligent request queuing');
            }

            // Provider-specific tips
            if (provider.name === 'Google' && numStores > 50) {
                optimizations.push('🔑 Google Gemini has low rate limits - consider multiple API keys');
            }

            if (provider.name === 'DeepSeek') {
                optimizations.push('🇨🇳 DeepSeek offers excellent value with context caching for repeated prompts');
            }

            if (provider.name === 'Alibaba') {
                optimizations.push('🌐 Qwen excels in multilingual scenarios and enterprise scaling');
            }

            optimizations.forEach(tip => {
                const li = document.createElement('li');
                li.textContent = tip;
                tips.appendChild(li);
            });
        }

        function simulateTextCost() {
            const inputText = document.getElementById('inputText').value;
            const outputText = document.getElementById('outputText').value;
            
            if (!inputText.trim() || !outputText.trim()) {
                alert('Please enter both input and output text to simulate costs.');
                return;
            }

            const inputTokens = estimateTokens(inputText);
            const outputTokens = estimateTokens(outputText);
            const totalTokens = inputTokens + outputTokens;

            // Update token display
            document.getElementById('simInputTokens').textContent = inputTokens;
            document.getElementById('simOutputTokens').textContent = outputTokens;
            document.getElementById('simTotalTokens').textContent = totalTokens;

            // Calculate cost for primary provider
            const primaryProviderId = document.getElementById('primaryProvider').value;
            const primaryProvider = llmProviders[primaryProviderId];
            const batchProcessing = document.getElementById('batchProcessing').value === 'true';
            const primaryCost = calculateCostPerResponse(primaryProvider, inputTokens, outputTokens, batchProcessing);
            
            document.getElementById('simCostPerResponse').textContent = formatCurrency(primaryCost);

            // Update simulator table with all providers
            const tbody = document.getElementById('simulatorTableBody');
            tbody.innerHTML = '';

            const providerCosts = [];
            Object.entries(llmProviders).forEach(([id, provider]) => {
                const costPerResponse = calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing);
                const dailyCost = costPerResponse * 100 * 30; // 100 stores, 30 responses/day
                
                providerCosts.push({
                    provider,
                    costPerResponse,
                    dailyCost
                });
            });

            // Sort by cost per response
            providerCosts.sort((a, b) => a.costPerResponse - b.costPerResponse);

            providerCosts.forEach(({ provider, costPerResponse, dailyCost }) => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${provider.name}</td>
                    <td>${provider.model}</td>
                    <td class="${getCostClass(costPerResponse)}">${formatCurrency(costPerResponse)}</td>
                    <td class="${getCostClass(dailyCost)}">${formatCurrency(dailyCost)}</td>
                `;
            });

            // Show results
            document.getElementById('simulatorResults').style.display = 'block';
        }

        // Initialize with default calculation
        calculateCosts();

        // Add event listeners for real-time updates
        ['numStores', 'responsesPerDay', 'inputTokens', 'outputTokens', 'primaryProvider', 'batchProcessing'].forEach(id => {
            document.getElementById(id).addEventListener('change', calculateCosts);
            document.getElementById(id).addEventListener('input', calculateCosts);
        });
    </script>
</body>
</html>