<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM API Cost Calculator - CPR Store Review Automation</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .calculator-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .results-section {
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .text-simulator {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            padding: 0;
            border-radius: 8px;
            margin-top: 30px;
            grid-column: 1 / -1;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            position: relative;
        }

        .simulator-header {
            background: #f8fafc;
            border-bottom: 2px solid #e5e7eb;
            padding: 24px;
            text-align: center;
        }

        .simulator-content {
            background: #ffffff;
            padding: 32px;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            font-family: inherit;
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: 1px solid #3b82f6;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            background: #2563eb;
            border-color: #2563eb;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .btn-secondary {
            background: #6b7280;
            border-color: #6b7280;
            margin-top: 12px;
        }

        .btn-secondary:hover {
            background: #4b5563;
            border-color: #4b5563;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .result-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .result-card h3 {
            font-size: 0.9em;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .result-card .value {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .provider-comparison {
            margin-top: 25px;
        }
        
        .provider-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .provider-table th {
            background: #f9fafb;
            color: #374151;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            border-bottom: 2px solid #e5e7eb;
            font-size: 0.875em;
        }

        .provider-table td {
            padding: 14px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
        }

        .provider-table tr:hover {
            background: #f9fafb;
        }
        
        .cost-low { color: #059669; font-weight: 600; }
        .cost-medium { color: #d97706; font-weight: 600; }
        .cost-high { color: #dc2626; font-weight: 600; }
        
        .chart-container {
            margin-top: 25px;
            height: 400px;
            position: relative;
        }
        
        .optimization-tips {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 20px;
            margin-top: 25px;
            border-radius: 0 8px 8px 0;
        }
        
        .optimization-tips h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }
        
        .optimization-tips ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .optimization-tips li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .optimization-tips li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }
        
        .text-simulator h3 {
            color: #1f2937;
            margin-bottom: 8px;
            text-align: center;
            font-size: 1.75em;
            font-weight: 600;
            letter-spacing: -0.025em;
        }

        .simulator-subtitle {
            color: #6b7280;
            font-size: 1em;
            margin-bottom: 0;
            font-weight: 400;
        }
        
        .simulator-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .input-container {
            position: relative;
            background: #ffffff;
            border-radius: 8px;
            padding: 24px;
            border: 1px solid #d1d5db;
            transition: all 0.2s ease;
        }

        .input-container:hover {
            border-color: #9ca3af;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .input-container.focused {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
            font-weight: 600;
            color: #374151;
            font-size: 0.95em;
        }

        .token-counter {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 0.85em;
            font-weight: 600;
            min-width: 80px;
            text-align: center;
        }

        .char-counter {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
        
        .simulator-results {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            box-shadow: 0 10px 25px rgba(0,0,0,0.05);
            margin-top: 25px;
            animation: slideInUp 0.5s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100px);
            }
        }

        .results-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .results-title {
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .results-subtitle {
            color: #666;
            font-size: 0.95em;
        }
        
        .token-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .token-card {
            background: #ffffff;
            padding: 24px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e5e7eb;
            color: #374151;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .token-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .token-card h4 {
            color: #6b7280;
            margin-bottom: 8px;
            font-size: 0.875em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .token-card .token-value {
            font-size: 2em;
            font-weight: 700;
            color: #1f2937;
            position: relative;
            z-index: 1;
        }

        .token-card.cost-card {
            border-left: 4px solid #10b981;
        }

        .token-card.cost-card .token-value {
            color: #059669;
        }

        .token-card.total-card {
            border-left: 4px solid #f59e0b;
        }

        .token-card.total-card .token-value {
            color: #d97706;
        }

        .tier-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7em;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 6px;
            letter-spacing: 0.025em;
        }

        .tier-ultrabudget { background: #10b981; color: white; }
        .tier-budget { background: #3b82f6; color: white; }
        .tier-standard { background: #f59e0b; color: white; }
        .tier-premium { background: #ef4444; color: white; }
        .tier-enterprise { background: #8b5cf6; color: white; }

        .analysis-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }

        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #27ae60;
        }

        .export-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            margin-left: 10px;
        }

        .efficiency-meter {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 25px 0;
            border: 2px solid #e9ecef;
        }

        .efficiency-bar {
            position: relative;
            background: #e9ecef;
            height: 30px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .efficiency-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c 0%, #f39c12 50%, #27ae60 100%);
            border-radius: 15px;
            transition: width 0.8s ease;
            position: relative;
        }

        .efficiency-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
            z-index: 2;
        }

        .efficiency-tips {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            font-size: 0.9em;
            color: #2c3e50;
        }

        .provider-comparison-enhanced {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .provider-comparison-enhanced .provider-table {
            margin: 0;
            border: none;
        }

        .provider-comparison-enhanced .provider-table th {
            background: #f9fafb;
            color: #374151;
            font-size: 0.875em;
            padding: 16px;
            border-bottom: 2px solid #e5e7eb;
        }

        .provider-comparison-enhanced .provider-table td {
            padding: 16px;
            vertical-align: middle;
        }

        .provider-comparison-enhanced .provider-table tr:nth-child(1) {
            background: #f0fdf4;
            border-left: 3px solid #10b981;
        }

        .provider-comparison-enhanced .provider-table tr:nth-child(2) {
            background: #fffbeb;
            border-left: 3px solid #f59e0b;
        }

        .provider-comparison-enhanced .provider-table tr:nth-child(3) {
            background: #fef2f2;
            border-left: 3px solid #ef4444;
        }

        .rank-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            color: white;
            font-size: 0.75em;
            text-align: center;
            min-width: 24px;
        }

        .rank-1 { background: #10b981; }
        .rank-2 { background: #f59e0b; }
        .rank-3 { background: #ef4444; }
        .rank-other { background: #6b7280; }

        .speed-indicator {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .speed-ultra-fast { background: #27ae60; color: white; }
        .speed-very-fast { background: #2ecc71; color: white; }
        .speed-fast { background: #f39c12; color: white; }
        .speed-medium { background: #e67e22; color: white; }
        .speed-slow { background: #e74c3c; color: white; }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }

            .simulator-grid {
                grid-template-columns: 1fr;
            }

            .tier-badge {
                display: block;
                margin: 2px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Advanced LLM Cost Calculator</h1>
            <p>CPR Store Review Response Automation - Enterprise-Grade Cost Analysis & ROI Tool</p>
            <p style="font-size: 0.9em; opacity: 0.8;">✅ 2025 Pricing Data | ✅ 15+ Providers | ✅ Context Caching | ✅ Batch Processing | ✅ Rate Limit Analysis</p>
        </div>
        
        <div class="main-content">
            <div class="calculator-section">
                <h2>Configuration Parameters</h2>
                
                <div class="form-group">
                    <label for="numStores">Number of CPR Stores</label>
                    <input type="number" id="numStores" value="100" min="1" max="1000">
                </div>
                
                <div class="form-group">
                    <label for="responsesPerDay">Responses per Store per Day</label>
                    <input type="number" id="responsesPerDay" value="30" min="1" max="100">
                </div>
                
                <div class="form-group">
                    <label for="inputTokens">Input Tokens per Response</label>
                    <input type="number" id="inputTokens" value="45" min="10" max="200">
                </div>
                
                <div class="form-group">
                    <label for="outputTokens">Output Tokens per Response</label>
                    <input type="number" id="outputTokens" value="68" min="20" max="150">
                </div>
                
                <div class="form-group">
                    <label for="primaryProvider">Primary LLM Provider</label>
                    <select id="primaryProvider">
                        <optgroup label="🏆 ULTRA-BUDGET (Best Value)">
                            <option value="groq-llama-3.1-8b">Groq Llama 3.1 8B - $0.000008/response (FASTEST)</option>
                            <option value="groq-llama-3-8b">Groq Llama 3 8B - $0.000008/response (1345 tok/s)</option>
                        </optgroup>
                        <optgroup label="💰 BUDGET TIER">
                            <option value="gemini-2.0-flash-lite">Google Gemini 2.0 Flash Lite - $0.000025/response</option>
                            <option value="openai-gpt-4.1-nano">OpenAI GPT-4.1 Nano - $0.000032/response</option>
                            <option value="gemini-2.0-flash">Google Gemini 2.0 Flash - $0.000039/response</option>
                        </optgroup>
                        <optgroup label="⚖️ STANDARD TIER (Balanced)">
                            <option value="deepseek-v3-offpeak">DeepSeek V3 Off-Peak (50% Off) - $0.000043/response</option>
                            <option value="groq-gemma-2-9b">Groq Gemma 2 9B - $0.000023/response</option>
                            <option value="deepseek-v3">DeepSeek V3 Standard - $0.000087/response</option>
                            <option value="gemini-2.5-flash">Google Gemini 2.5 Flash (Latest) - $0.000184/response</option>
                            <option value="openai-gpt-4.1-mini">OpenAI GPT-4.1 Mini - $0.000127/response</option>
                            <option value="groq-llama-3.3-70b">Groq Llama 3.3 70B - $0.000080/response</option>
                            <option value="claude-haiku-3.5">Claude Haiku 3.5 (Fastest Claude) - $0.000308/response</option>
                        </optgroup>
                        <optgroup label="🚀 PREMIUM TIER (Advanced)">
                            <option value="gemini-2.5-pro">Google Gemini 2.5 Pro (Latest) - $0.000736/response</option>
                            <option value="openai-gpt-4.1">OpenAI GPT-4.1 (Most Capable) - $0.000634/response</option>
                            <option value="claude-sonnet-4">Claude Sonnet 4 (Latest) - $0.001155/response</option>
                            <option value="openai-o4-mini">OpenAI o4-mini (Reasoning) - $0.000349/response</option>
                        </optgroup>
                        <optgroup label="🏢 ENTERPRISE TIER">
                            <option value="claude-opus-4">Claude Opus 4 (Most Capable) - $0.005775/response</option>
                        </optgroup>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="batchProcessing">Enable Batch Processing (where available)</label>
                    <select id="batchProcessing">
                        <option value="false">No</option>
                        <option value="true">Yes (Up to 50% savings)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="contextCaching">Enable Context Caching (for repeated prompts)</label>
                    <select id="contextCaching">
                        <option value="false">No</option>
                        <option value="true">Yes (75-90% input cost reduction)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="analysisMode">Analysis Mode</label>
                    <select id="analysisMode">
                        <option value="basic">Basic Cost Analysis</option>
                        <option value="advanced">Advanced (Rate Limits + Optimization)</option>
                        <option value="enterprise">Enterprise (ROI + Risk Analysis)</option>
                    </select>
                </div>

                <button class="btn" onclick="calculateCosts()">Calculate Comprehensive Costs</button>
                <button class="btn btn-secondary" onclick="exportReport()">📊 Export Executive Report</button>
            </div>
            
            <div class="results-section">
                <h2>Cost Analysis Results</h2>
                
                <div class="results-grid" id="resultsGrid">
                    <div class="result-card">
                        <h3>Cost per Response</h3>
                        <div class="value" id="costPerResponse">$0.000</div>
                    </div>
                    <div class="result-card">
                        <h3>Daily Cost</h3>
                        <div class="value" id="dailyCost">$0.00</div>
                    </div>
                    <div class="result-card">
                        <h3>Monthly Cost</h3>
                        <div class="value" id="monthlyCost">$0.00</div>
                    </div>
                    <div class="result-card">
                        <h3>Annual Cost</h3>
                        <div class="value" id="annualCost">$0.00</div>
                    </div>
                </div>

                <div class="analysis-section" id="executiveSummary" style="display: none;">
                    <h3>📋 Executive Summary</h3>
                    <div id="summaryContent"></div>
                </div>
                
                <div class="provider-comparison">
                    <h3>Provider Comparison (Ranked by Cost - Current Configuration)</h3>
                    <table class="provider-table" id="providerTable">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Provider</th>
                                <th>Model</th>
                                <th>Cost/Response</th>
                                <th>Monthly Cost</th>
                                <th>Rate Limit (RPM)</th>
                            </tr>
                        </thead>
                        <tbody id="providerTableBody">
                        </tbody>
                    </table>
                </div>
                
                <div class="chart-container">
                    <canvas id="costChart"></canvas>
                </div>
                
                <div class="optimization-tips">
                    <h3>Cost Optimization Recommendations</h3>
                    <ul id="optimizationTips">
                    </ul>
                </div>
            </div>
            
            <div class="text-simulator">
                <div class="simulator-header">
                    <h3>🧮 Advanced Text Cost Simulator</h3>
                    <p class="simulator-subtitle">
                        Real-time token analysis with live cost calculations across all providers
                    </p>
                </div>

                <div class="simulator-content">
                    <div class="simulator-grid">
                        <div class="input-container" id="inputContainer">
                            <div class="input-label">
                                <span>📝 Input Text (Review + System Prompt)</span>
                                <span class="token-counter" id="inputTokenCounter">0 tokens</span>
                            </div>
                            <textarea id="inputText" placeholder="Example: You are a customer service AI for CPR cell phone repair. Please respond to this customer review: 'Great service! Fixed my iPhone screen quickly and professionally. Highly recommend!'" oninput="updateTokenCounts()" onfocus="focusInput('input')" onblur="blurInput('input')">You are a customer service AI for CPR cell phone repair. Please respond to this customer review: "Great service! Fixed my iPhone screen quickly and professionally. Highly recommend!"</textarea>
                            <div class="char-counter" id="inputCharCounter">0 characters</div>
                        </div>

                        <div class="input-container" id="outputContainer">
                            <div class="input-label">
                                <span>💬 Expected Output Text (Generated Response)</span>
                                <span class="token-counter" id="outputTokenCounter">0 tokens</span>
                            </div>
                            <textarea id="outputText" placeholder="Example response with CPR keywords..." oninput="updateTokenCounts()" onfocus="focusInput('output')" onblur="blurInput('output')">Thank you so much for your wonderful review! We're thrilled to hear about your positive experience with our iPhone screen repair service. At CPR, we pride ourselves on providing quick, professional phone screen repairs, battery replacements, and comprehensive device solutions. Your recommendation means the world to us! We also offer game console repairs, laptop screen repairs, and tablet fixes. Visit us again for any future phone repair needs!</textarea>
                            <div class="char-counter" id="outputCharCounter">0 characters</div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 32px 0; display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
                        <button class="btn" onclick="simulateTextCost()" style="background: #3b82f6; border-color: #3b82f6; font-size: 1em; padding: 12px 24px; width: auto; min-width: 180px;">
                            ⚡ Calculate Live Costs
                        </button>
                        <button class="btn btn-secondary" onclick="loadTemplate()" style="width: auto; min-width: 140px;">
                            📋 Load Template
                        </button>
                        <button class="btn btn-secondary" onclick="optimizeText()" style="width: auto; min-width: 140px;">
                            🎯 Optimize Tokens
                        </button>
                    </div>
                
                    <div class="simulator-results" id="simulatorResults" style="display: none;">
                        <div class="results-header">
                            <div class="results-title">📊 Live Cost Analysis</div>
                            <div class="results-subtitle">Real-time token breakdown and provider comparison</div>
                        </div>

                        <div class="token-info">
                            <div class="token-card">
                                <h4>📝 Input Tokens</h4>
                                <div class="token-value" id="simInputTokens">0</div>
                            </div>
                            <div class="token-card">
                                <h4>💬 Output Tokens</h4>
                                <div class="token-value" id="simOutputTokens">0</div>
                            </div>
                            <div class="token-card total-card">
                                <h4>🔢 Total Tokens</h4>
                                <div class="token-value" id="simTotalTokens">0</div>
                            </div>
                            <div class="token-card cost-card">
                                <h4>💰 Selected Cost</h4>
                                <div class="token-value" id="simCostPerResponse">$0.000</div>
                            </div>
                        </div>

                        <div class="efficiency-meter" id="efficiencyMeter" style="display: none;">
                            <h4 style="text-align: center; margin-bottom: 15px; color: #2c3e50;">⚡ Token Efficiency Score</h4>
                            <div class="efficiency-bar">
                                <div class="efficiency-fill" id="efficiencyFill"></div>
                                <div class="efficiency-text" id="efficiencyText">0%</div>
                            </div>
                            <div class="efficiency-tips" id="efficiencyTips"></div>
                        </div>

                        <h4 style="margin-top: 30px; margin-bottom: 20px; color: #2c3e50; text-align: center; font-size: 1.3em;">
                            🏆 Provider Cost Comparison
                        </h4>
                        <div class="cost-visualization" id="costVisualization" style="margin-bottom: 25px;">
                            <canvas id="simulatorChart" width="800" height="300"></canvas>
                        </div>

                        <div class="provider-comparison-enhanced">
                            <table class="provider-table" id="simulatorTable">
                                <thead>
                                    <tr>
                                        <th>🏅 Rank</th>
                                        <th>🏢 Provider</th>
                                        <th>🤖 Model</th>
                                        <th>💵 Cost/Response</th>
                                        <th>📈 Daily Cost</th>
                                        <th>⚡ Speed</th>
                                    </tr>
                                </thead>
                                <tbody id="simulatorTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // LLM Provider Data (Updated with accurate 2025 pricing - Ranked from most economic to most expensive)
        const llmProviders = {
            // ULTRA-BUDGET TIER (Under $0.10 per response)
            'groq-llama-3.1-8b': {
                name: 'Groq',
                model: 'Llama 3.1 8B Instant',
                inputPrice: 0.05,
                outputPrice: 0.08,
                rateLimit: 30000,
                batchDiscount: 0.25,
                contextCaching: false,
                tier: 'Ultra-Budget',
                speed: 'Ultra-Fast (840 tok/s)',
                notes: 'Best value for simple responses'
            },
            'groq-llama-3-8b': {
                name: 'Groq',
                model: 'Llama 3 8B',
                inputPrice: 0.05,
                outputPrice: 0.08,
                rateLimit: 30000,
                batchDiscount: 0.25,
                contextCaching: false,
                tier: 'Ultra-Budget',
                speed: 'Ultra-Fast (1345 tok/s)',
                notes: 'Fastest inference available'
            },
            'gemini-2.0-flash-lite': {
                name: 'Google',
                model: 'Gemini 2.0 Flash Lite',
                inputPrice: 0.075,
                outputPrice: 0.30,
                rateLimit: 60,
                batchDiscount: 0,
                contextCaching: false,
                tier: 'Budget',
                speed: 'Fast',
                notes: 'Low rate limits, good for testing'
            },
            'openai-gpt-4.1-nano': {
                name: 'OpenAI',
                model: 'GPT-4.1 Nano',
                inputPrice: 0.100,
                outputPrice: 0.400,
                rateLimit: 30000,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Budget',
                speed: 'Very Fast',
                notes: 'Fastest OpenAI model, great for simple tasks'
            },
            'gemini-2.0-flash': {
                name: 'Google',
                model: 'Gemini 2.0 Flash',
                inputPrice: 0.10,
                outputPrice: 0.50,
                rateLimit: 60,
                batchDiscount: 0,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Budget',
                speed: 'Fast',
                notes: 'Multimodal, 1M context, agent-ready'
            },

            // STANDARD TIER ($0.10-$1.00 per response)
            'deepseek-v3-offpeak': {
                name: 'DeepSeek',
                model: 'V3 (Off-Peak 50% Off)',
                inputPrice: 0.14,
                outputPrice: 0.55,
                rateLimit: 5000,
                batchDiscount: 0,
                contextCaching: true,
                cachingDiscount: 0.90,
                tier: 'Standard',
                speed: 'Fast',
                notes: 'UTC 16:30-00:30 only, excellent value'
            },
            'groq-gemma-2-9b': {
                name: 'Groq',
                model: 'Gemma 2 9B',
                inputPrice: 0.20,
                outputPrice: 0.20,
                rateLimit: 5000,
                batchDiscount: 0.25,
                contextCaching: false,
                tier: 'Standard',
                speed: 'Very Fast (500 tok/s)',
                notes: 'Google model on Groq infrastructure'
            },
            'deepseek-v3': {
                name: 'DeepSeek',
                model: 'V3 (Standard)',
                inputPrice: 0.27,
                outputPrice: 1.10,
                rateLimit: 5000,
                batchDiscount: 0,
                contextCaching: true,
                cachingDiscount: 0.90,
                tier: 'Standard',
                speed: 'Fast',
                notes: 'Excellent reasoning, context caching'
            },
            'gemini-2.5-flash': {
                name: 'Google',
                model: 'Gemini 2.5 Flash',
                inputPrice: 0.30,
                outputPrice: 2.50,
                rateLimit: 60,
                batchDiscount: 0,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Standard',
                speed: 'Fast',
                notes: 'Latest model, 1M context, thinking mode'
            },
            'openai-gpt-4.1-mini': {
                name: 'OpenAI',
                model: 'GPT-4.1 Mini',
                inputPrice: 0.40,
                outputPrice: 1.60,
                rateLimit: 10000,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Standard',
                speed: 'Fast',
                notes: 'Balanced performance and cost'
            },
            'groq-llama-3.3-70b': {
                name: 'Groq',
                model: 'Llama 3.3 70B Versatile',
                inputPrice: 0.59,
                outputPrice: 0.79,
                rateLimit: 5000,
                batchDiscount: 0.25,
                contextCaching: false,
                tier: 'Standard',
                speed: 'Fast (394 tok/s)',
                notes: 'Large model with fast inference'
            },
            'claude-haiku-3.5': {
                name: 'Anthropic',
                model: 'Claude Haiku 3.5',
                inputPrice: 0.80,
                outputPrice: 4.00,
                rateLimit: 1000,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.90,
                tier: 'Standard',
                speed: 'Very Fast',
                notes: 'Fastest Claude model, excellent for automation'
            },

            // PREMIUM TIER ($1.00+ per response)
            'gemini-2.5-pro': {
                name: 'Google',
                model: 'Gemini 2.5 Pro',
                inputPrice: 1.25,
                outputPrice: 10.00,
                rateLimit: 60,
                batchDiscount: 0,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Premium',
                speed: 'Medium',
                notes: 'Advanced reasoning, coding excellence'
            },
            'openai-gpt-4.1': {
                name: 'OpenAI',
                model: 'GPT-4.1',
                inputPrice: 2.00,
                outputPrice: 8.00,
                rateLimit: 10000,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Premium',
                speed: 'Medium',
                notes: 'Most capable OpenAI model'
            },
            'claude-sonnet-4': {
                name: 'Anthropic',
                model: 'Claude Sonnet 4',
                inputPrice: 3.00,
                outputPrice: 15.00,
                rateLimit: 1000,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.90,
                tier: 'Premium',
                speed: 'Medium',
                notes: 'Latest Claude, exceptional reasoning'
            },
            'openai-o4-mini': {
                name: 'OpenAI',
                model: 'o4-mini (Reasoning)',
                inputPrice: 1.10,
                outputPrice: 4.40,
                rateLimit: 5000,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.75,
                tier: 'Premium',
                speed: 'Slow (Reasoning)',
                notes: 'Advanced reasoning for complex problems'
            },
            'claude-opus-4': {
                name: 'Anthropic',
                model: 'Claude Opus 4',
                inputPrice: 15.00,
                outputPrice: 75.00,
                rateLimit: 500,
                batchDiscount: 0.50,
                contextCaching: true,
                cachingDiscount: 0.90,
                tier: 'Enterprise',
                speed: 'Slow',
                notes: 'Most capable model available, enterprise use'
            }
        };

        let costChart = null;

        // Legacy token estimation function (kept for compatibility)
        function estimateTokens(text) {
            // Fallback to advanced estimation
            return estimateTokensAdvanced(text);
        }

        // Input validation function
        function validateInputs() {
            const numStores = parseInt(document.getElementById('numStores').value);
            const responsesPerDay = parseInt(document.getElementById('responsesPerDay').value);
            const inputTokens = parseInt(document.getElementById('inputTokens').value);
            const outputTokens = parseInt(document.getElementById('outputTokens').value);

            const errors = [];

            if (numStores < 1 || numStores > 10000) {
                errors.push('Number of stores must be between 1 and 10,000');
            }

            if (responsesPerDay < 1 || responsesPerDay > 1000) {
                errors.push('Responses per day must be between 1 and 1,000');
            }

            if (inputTokens < 10 || inputTokens > 100000) {
                errors.push('Input tokens must be between 10 and 100,000');
            }

            if (outputTokens < 10 || outputTokens > 100000) {
                errors.push('Output tokens must be between 10 and 100,000');
            }

            return errors;
        }

        function calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing, contextCaching = false) {
            let inputCost = (inputTokens * provider.inputPrice) / 1000000;
            const outputCost = (outputTokens * provider.outputPrice) / 1000000;

            // Apply context caching discount to input tokens (if available)
            if (contextCaching && provider.contextCaching && provider.cachingDiscount) {
                inputCost = inputCost * (1 - provider.cachingDiscount);
            }

            let totalCost = inputCost + outputCost;

            // Apply batch processing discount
            if (batchProcessing && provider.batchDiscount > 0) {
                totalCost = totalCost * (1 - provider.batchDiscount);
            }

            return totalCost;
        }

        // Enhanced token estimation with better accuracy
        function estimateTokensAdvanced(text) {
            // More accurate token estimation based on actual tokenizer patterns
            // Account for punctuation, spaces, and common patterns
            const words = text.trim().split(/\s+/);
            const avgTokensPerWord = 1.3; // More realistic ratio
            const baseTokens = words.length * avgTokensPerWord;

            // Add tokens for punctuation and formatting
            const punctuationTokens = (text.match(/[.,!?;:()[\]{}]/g) || []).length * 0.5;
            const numberTokens = (text.match(/\d+/g) || []).length * 0.8;

            return Math.ceil(baseTokens + punctuationTokens + numberTokens);
        }

        function formatCurrency(amount) {
            if (amount < 0.01) {
                return `$${amount.toFixed(6)}`;
            }
            return `$${amount.toFixed(2)}`;
        }

        function getCostClass(cost) {
            if (cost < 0.00005) return 'cost-low';
            if (cost < 0.0002) return 'cost-medium';
            return 'cost-high';
        }

        function getSpeedClass(speedText) {
            const speed = speedText.toLowerCase();
            if (speed.includes('ultra-fast')) return 'speed-ultra-fast';
            if (speed.includes('very fast')) return 'speed-very-fast';
            if (speed.includes('fast')) return 'speed-fast';
            if (speed.includes('medium')) return 'speed-medium';
            return 'speed-slow';
        }

        function calculateCosts() {
            // Validate inputs first
            const errors = validateInputs();
            if (errors.length > 0) {
                alert('Please fix the following errors:\n\n' + errors.join('\n'));
                return;
            }

            const numStores = parseInt(document.getElementById('numStores').value);
            const responsesPerDay = parseInt(document.getElementById('responsesPerDay').value);
            const inputTokens = parseInt(document.getElementById('inputTokens').value);
            const outputTokens = parseInt(document.getElementById('outputTokens').value);
            const primaryProviderId = document.getElementById('primaryProvider').value;
            const batchProcessing = document.getElementById('batchProcessing').value === 'true';
            const contextCaching = document.getElementById('contextCaching').value === 'true';
            const analysisMode = document.getElementById('analysisMode').value;

            const primaryProvider = llmProviders[primaryProviderId];
            const costPerResponse = calculateCostPerResponse(primaryProvider, inputTokens, outputTokens, batchProcessing, contextCaching);
            const dailyCost = costPerResponse * numStores * responsesPerDay;
            const monthlyCost = dailyCost * 30;
            const annualCost = dailyCost * 365;

            // Update main results
            document.getElementById('costPerResponse').textContent = formatCurrency(costPerResponse);
            document.getElementById('dailyCost').textContent = formatCurrency(dailyCost);
            document.getElementById('monthlyCost').textContent = formatCurrency(monthlyCost);
            document.getElementById('annualCost').textContent = formatCurrency(annualCost);

            // Update provider comparison table
            updateProviderTable(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing, contextCaching);

            // Update chart
            updateCostChart(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing, contextCaching);

            // Update optimization tips
            updateOptimizationTips(numStores, responsesPerDay, costPerResponse, primaryProvider, analysisMode);

            // Add rate limit analysis for advanced/enterprise modes
            if (analysisMode !== 'basic') {
                updateRateLimitAnalysis(numStores, responsesPerDay, primaryProvider);
            }

            // Add ROI analysis for enterprise mode
            if (analysisMode === 'enterprise') {
                updateROIAnalysis(numStores, responsesPerDay, costPerResponse, annualCost);
                updateExecutiveSummary(numStores, responsesPerDay, costPerResponse, annualCost, primaryProvider);
            }
        }

        // Executive summary function
        function updateExecutiveSummary(numStores, responsesPerDay, costPerResponse, annualCost, provider) {
            const totalResponses = numStores * responsesPerDay * 365;
            const avgResponseTime = 5; // minutes
            const hourlyWage = 15;
            const manualCostPerResponse = (avgResponseTime / 60) * hourlyWage;
            const annualManualCost = manualCostPerResponse * totalResponses;
            const annualSavings = annualManualCost - annualCost;

            const summaryHtml = `
                <div style="background: white; padding: 20px; border-radius: 8px; border: 2px solid #27ae60;">
                    <h4 style="color: #27ae60; margin-bottom: 15px;">💼 Business Case Summary</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
                        <div>
                            <strong>Scale:</strong> ${numStores.toLocaleString()} stores<br>
                            <strong>Volume:</strong> ${totalResponses.toLocaleString()} responses/year<br>
                            <strong>Provider:</strong> ${provider.name} ${provider.model}
                        </div>
                        <div>
                            <strong>Annual Cost:</strong> ${formatCurrency(annualCost)}<br>
                            <strong>Annual Savings:</strong> ${formatCurrency(annualSavings)}<br>
                            <strong>ROI:</strong> ${((annualSavings / annualCost) * 100).toFixed(0)}%
                        </div>
                    </div>
                    <div style="background: #e8f5e8; padding: 10px; border-radius: 5px;">
                        <strong>Recommendation:</strong> ${getRecommendation(provider, costPerResponse, annualSavings)}
                    </div>
                </div>
            `;

            document.getElementById('summaryContent').innerHTML = summaryHtml;
            document.getElementById('executiveSummary').style.display = 'block';
        }

        function getRecommendation(provider, costPerResponse, annualSavings) {
            if (annualSavings > 100000) {
                return `Excellent ROI potential. ${provider.name} ${provider.model} offers substantial cost savings with ${provider.tier.toLowerCase()} tier pricing. Recommend immediate implementation.`;
            } else if (annualSavings > 50000) {
                return `Strong business case. ${provider.name} provides good value in the ${provider.tier.toLowerCase()} tier. Consider pilot program.`;
            } else if (annualSavings > 10000) {
                return `Moderate savings potential. Evaluate against implementation costs and consider starting with high-volume stores.`;
            } else {
                return `Limited savings with current configuration. Consider optimizing parameters or evaluating manual vs. automated trade-offs.`;
            }
        }

        function updateProviderTable(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing, contextCaching) {
            const tbody = document.getElementById('providerTableBody');
            tbody.innerHTML = '';

            const providerCosts = [];

            Object.entries(llmProviders).forEach(([id, provider]) => {
                const costPerResponse = calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing, contextCaching);
                const monthlyCost = costPerResponse * numStores * responsesPerDay * 30;

                // Calculate potential savings
                const baseCost = calculateCostPerResponse(provider, inputTokens, outputTokens, false, false);
                const savingsPercent = ((baseCost - costPerResponse) / baseCost * 100).toFixed(1);

                providerCosts.push({
                    id,
                    provider,
                    costPerResponse,
                    monthlyCost,
                    savingsPercent: savingsPercent > 0 ? savingsPercent : 0
                });
            });

            // Sort by cost per response
            providerCosts.sort((a, b) => a.costPerResponse - b.costPerResponse);

            providerCosts.forEach(({ provider, costPerResponse, monthlyCost, savingsPercent }, index) => {
                const row = tbody.insertRow();
                const savingsDisplay = savingsPercent > 0 ? `<span style="color: #27ae60;">(-${savingsPercent}%)</span>` : '';
                const tierBadge = `<span class="tier-badge tier-${provider.tier.toLowerCase().replace('-', '')}">${provider.tier}</span>`;

                row.innerHTML = `
                    <td><strong>#${index + 1}</strong></td>
                    <td>${provider.name} ${tierBadge}</td>
                    <td>${provider.model}<br><small style="color: #666;">${provider.speed}</small></td>
                    <td class="${getCostClass(costPerResponse)}">${formatCurrency(costPerResponse)} ${savingsDisplay}</td>
                    <td class="${getCostClass(monthlyCost)}">${formatCurrency(monthlyCost)}</td>
                    <td>${provider.rateLimit.toLocaleString()}<br><small style="color: #666;">RPM</small></td>
                `;
            });
        }

        function updateCostChart(numStores, responsesPerDay, inputTokens, outputTokens, batchProcessing, contextCaching) {
            const ctx = document.getElementById('costChart').getContext('2d');
            
            if (costChart) {
                costChart.destroy();
            }

            const storeScales = [10, 25, 50, 100, 200, 300];
            const datasets = [];

            // Get top 6 most cost-effective providers
            const providerCosts = Object.entries(llmProviders).map(([id, provider]) => ({
                id,
                provider,
                costPerResponse: calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing, contextCaching)
            })).sort((a, b) => a.costPerResponse - b.costPerResponse).slice(0, 6);

            const colors = ['#10b981', '#f59e0b', '#3b82f6', '#ef4444', '#8b5cf6', '#06b6d4'];

            providerCosts.forEach((item, index) => {
                const data = storeScales.map(stores => {
                    return item.costPerResponse * stores * responsesPerDay * 30;
                });

                datasets.push({
                    label: `${item.provider.name} ${item.provider.model}`,
                    data: data,
                    borderColor: colors[index],
                    backgroundColor: colors[index] + '20',
                    tension: 0.4
                });
            });

            costChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: storeScales.map(s => `${s} Stores`),
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Monthly Cost Scaling by Number of Stores (Top 6 Most Economic)'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Monthly Cost ($)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Number of Stores'
                            }
                        }
                    }
                }
            });
        }

        function updateOptimizationTips(numStores, responsesPerDay, costPerResponse, provider, analysisMode = 'basic') {
            const tips = document.getElementById('optimizationTips');
            tips.innerHTML = '';

            const totalDailyRequests = numStores * responsesPerDay;
            const maxDailyRequests = provider.rateLimit * 60 * 24;

            const optimizations = [];

            // Rate limit warnings
            if (totalDailyRequests > maxDailyRequests * 0.8) {
                optimizations.push(`⚠️ Approaching rate limit! Current: ${totalDailyRequests.toLocaleString()}, Max: ${maxDailyRequests.toLocaleString()}`);
            }

            // Cost optimization suggestions
            if (costPerResponse > 0.00002) {
                optimizations.push('💡 Consider Groq Llama 3.1 8B for ultra-low costs ($0.000008/response)');
            }

            if (provider.name === 'DeepSeek' && !provider.model.includes('Off-Peak')) {
                optimizations.push('🕐 Use DeepSeek off-peak hours (UTC 16:30-00:30) for 50% savings');
            }

            if (provider.batchDiscount > 0 && document.getElementById('batchProcessing').value === 'false') {
                optimizations.push(`📦 Enable batch processing for ${(provider.batchDiscount * 100)}% cost savings`);
            }

            // Token optimization
            optimizations.push('✂️ Optimize prompts to reduce input tokens by 20-40%');
            optimizations.push('🎯 Set max_tokens parameter to limit output length');
            optimizations.push('📝 Use template-based responses for consistency');

            // Scaling recommendations
            if (numStores > 100) {
                optimizations.push('🔄 Consider multi-provider strategy for redundancy');
                optimizations.push('⚡ Implement intelligent request queuing');
            }

            // Provider-specific tips
            if (provider.name === 'Google' && numStores > 50) {
                optimizations.push('🔑 Google Gemini has low rate limits - consider multiple API keys');
            }

            if (provider.name === 'DeepSeek') {
                optimizations.push('🇨🇳 DeepSeek offers excellent value with context caching for repeated prompts');
            }

            if (provider.name === 'Alibaba') {
                optimizations.push('🌐 Qwen excels in multilingual scenarios and enterprise scaling');
            }

            optimizations.forEach(tip => {
                const li = document.createElement('li');
                li.textContent = tip;
                tips.appendChild(li);
            });
        }

        function simulateTextCost() {
            const inputText = document.getElementById('inputText').value;
            const outputText = document.getElementById('outputText').value;
            
            if (!inputText.trim() || !outputText.trim()) {
                alert('Please enter both input and output text to simulate costs.');
                return;
            }

            const inputTokens = estimateTokensAdvanced(inputText);
            const outputTokens = estimateTokensAdvanced(outputText);
            const totalTokens = inputTokens + outputTokens;

            // Update token display with animation
            animateValue('simInputTokens', 0, inputTokens, 500);
            animateValue('simOutputTokens', 0, outputTokens, 500);
            animateValue('simTotalTokens', 0, totalTokens, 500);

            // Calculate cost for primary provider
            const primaryProviderId = document.getElementById('primaryProvider').value;
            const primaryProvider = llmProviders[primaryProviderId];
            const batchProcessing = document.getElementById('batchProcessing').value === 'true';
            const contextCaching = document.getElementById('contextCaching').value === 'true';
            const primaryCost = calculateCostPerResponse(primaryProvider, inputTokens, outputTokens, batchProcessing, contextCaching);
            
            // Animate the cost value
            animateCurrency('simCostPerResponse', 0, primaryCost, 500);

            // Update simulator table with all providers
            const tbody = document.getElementById('simulatorTableBody');
            tbody.innerHTML = '';

            const providerCosts = [];
            Object.entries(llmProviders).forEach(([id, provider]) => {
                const costPerResponse = calculateCostPerResponse(provider, inputTokens, outputTokens, batchProcessing, contextCaching);
                const dailyCost = costPerResponse * 100 * 30; // 100 stores, 30 responses/day

                providerCosts.push({
                    provider,
                    costPerResponse,
                    dailyCost
                });
            });

            // Sort by cost per response
            providerCosts.sort((a, b) => a.costPerResponse - b.costPerResponse);

            providerCosts.forEach(({ provider, costPerResponse, dailyCost }, index) => {
                const row = tbody.insertRow();
                const rankBadge = index < 3 ? `rank-${index + 1}` : 'rank-other';
                const speedClass = getSpeedClass(provider.speed);

                row.innerHTML = `
                    <td><span class="rank-badge ${rankBadge}">#${index + 1}</span></td>
                    <td>${provider.name}<br><small style="color: #666;">${provider.tier}</small></td>
                    <td>${provider.model}</td>
                    <td class="${getCostClass(costPerResponse)}">${formatCurrency(costPerResponse)}</td>
                    <td class="${getCostClass(dailyCost)}">${formatCurrency(dailyCost)}</td>
                    <td><span class="speed-indicator ${speedClass}">${provider.speed.split(' ')[0]}</span></td>
                `;
            });

            // Update efficiency meter using the calculated values
            updateEfficiencyMeter(inputTokens, outputTokens, inputText.length, outputText.length);

            // Create cost visualization chart
            createSimulatorChart(providerCosts.slice(0, 6));

            // Show results
            document.getElementById('simulatorResults').style.display = 'block';
        }

        // Rate limit analysis function
        function updateRateLimitAnalysis(numStores, responsesPerDay, provider) {
            const totalDailyRequests = numStores * responsesPerDay;
            const maxDailyRequests = provider.rateLimit * 60 * 24; // RPM to daily
            const utilizationPercent = (totalDailyRequests / maxDailyRequests * 100).toFixed(1);

            let analysisHtml = `
                <div class="analysis-section">
                    <h3>📊 Rate Limit Analysis</h3>
                    <p><strong>Daily Requests:</strong> ${totalDailyRequests.toLocaleString()}</p>
                    <p><strong>Provider Limit:</strong> ${maxDailyRequests.toLocaleString()} requests/day</p>
                    <p><strong>Utilization:</strong> ${utilizationPercent}%</p>
            `;

            if (utilizationPercent > 80) {
                analysisHtml += `<div class="warning-box">⚠️ <strong>High Risk:</strong> You're using ${utilizationPercent}% of rate limits. Consider multiple API keys or alternative providers.</div>`;
            } else if (utilizationPercent > 50) {
                analysisHtml += `<div class="warning-box">⚠️ <strong>Medium Risk:</strong> Monitor usage closely. Consider backup providers.</div>`;
            } else {
                analysisHtml += `<div class="success-box">✅ <strong>Safe:</strong> Well within rate limits.</div>`;
            }

            analysisHtml += '</div>';

            // Add to results section
            const existingAnalysis = document.getElementById('rateLimitAnalysis');
            if (existingAnalysis) {
                existingAnalysis.innerHTML = analysisHtml;
            } else {
                const resultsSection = document.querySelector('.results-section');
                const div = document.createElement('div');
                div.id = 'rateLimitAnalysis';
                div.innerHTML = analysisHtml;
                resultsSection.appendChild(div);
            }
        }

        // ROI analysis function
        function updateROIAnalysis(numStores, responsesPerDay, costPerResponse, annualCost) {
            // Estimate value of automated responses
            const avgResponseTime = 5; // minutes per manual response
            const hourlyWage = 15; // USD per hour
            const manualCostPerResponse = (avgResponseTime / 60) * hourlyWage;
            const annualManualCost = manualCostPerResponse * numStores * responsesPerDay * 365;
            const annualSavings = annualManualCost - annualCost;
            const roiPercent = ((annualSavings / annualCost) * 100).toFixed(0);

            const analysisHtml = `
                <div class="analysis-section">
                    <h3>💰 ROI Analysis</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4>Manual Process</h4>
                            <p>Cost per response: ${formatCurrency(manualCostPerResponse)}</p>
                            <p>Annual cost: ${formatCurrency(annualManualCost)}</p>
                        </div>
                        <div>
                            <h4>AI Automation</h4>
                            <p>Cost per response: ${formatCurrency(costPerResponse)}</p>
                            <p>Annual cost: ${formatCurrency(annualCost)}</p>
                        </div>
                    </div>
                    <div class="success-box">
                        <strong>Annual Savings: ${formatCurrency(annualSavings)}</strong><br>
                        <strong>ROI: ${roiPercent}%</strong><br>
                        Payback period: ${(annualCost / (annualSavings / 12)).toFixed(1)} months
                    </div>
                </div>
            `;

            const existingROI = document.getElementById('roiAnalysis');
            if (existingROI) {
                existingROI.innerHTML = analysisHtml;
            } else {
                const resultsSection = document.querySelector('.results-section');
                const div = document.createElement('div');
                div.id = 'roiAnalysis';
                div.innerHTML = analysisHtml;
                resultsSection.appendChild(div);
            }
        }

        // Export report function
        function exportReport() {
            const numStores = parseInt(document.getElementById('numStores').value);
            const responsesPerDay = parseInt(document.getElementById('responsesPerDay').value);
            const primaryProviderId = document.getElementById('primaryProvider').value;
            const provider = llmProviders[primaryProviderId];

            const reportData = {
                timestamp: new Date().toISOString(),
                configuration: {
                    stores: numStores,
                    responsesPerDay: responsesPerDay,
                    provider: `${provider.name} ${provider.model}`
                },
                costs: {
                    perResponse: document.getElementById('costPerResponse').textContent,
                    daily: document.getElementById('dailyCost').textContent,
                    monthly: document.getElementById('monthlyCost').textContent,
                    annual: document.getElementById('annualCost').textContent
                }
            };

            // Create downloadable report
            const reportText = `
CPR STORE REVIEW AUTOMATION - COST ANALYSIS REPORT
Generated: ${new Date().toLocaleDateString()}

CONFIGURATION:
- Number of Stores: ${numStores}
- Responses per Store per Day: ${responsesPerDay}
- Selected Provider: ${provider.name} ${provider.model}
- Tier: ${provider.tier}

COST BREAKDOWN:
- Cost per Response: ${reportData.costs.perResponse}
- Daily Cost: ${reportData.costs.daily}
- Monthly Cost: ${reportData.costs.monthly}
- Annual Cost: ${reportData.costs.annual}

PROVIDER DETAILS:
- Speed: ${provider.speed}
- Rate Limit: ${provider.rateLimit.toLocaleString()} RPM
- Batch Processing: ${provider.batchDiscount > 0 ? 'Available' : 'Not Available'}
- Context Caching: ${provider.contextCaching ? 'Available' : 'Not Available'}

RECOMMENDATIONS:
${provider.notes}

This report was generated by the CPR LLM Cost Calculator.
            `;

            const blob = new Blob([reportText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `CPR_Cost_Analysis_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Real-time token counting functions
        function updateTokenCounts() {
            const inputText = document.getElementById('inputText').value;
            const outputText = document.getElementById('outputText').value;

            const inputTokens = estimateTokensAdvanced(inputText);
            const outputTokens = estimateTokensAdvanced(outputText);

            // Update token counters
            document.getElementById('inputTokenCounter').textContent = `${inputTokens} tokens`;
            document.getElementById('outputTokenCounter').textContent = `${outputTokens} tokens`;

            // Update character counters
            document.getElementById('inputCharCounter').textContent = `${inputText.length} characters`;
            document.getElementById('outputCharCounter').textContent = `${outputText.length} characters`;

            // Update efficiency if results are visible
            if (document.getElementById('simulatorResults').style.display !== 'none') {
                updateEfficiencyMeter(inputTokens, outputTokens, inputText.length, outputText.length);
            }
        }

        function focusInput(type) {
            const container = document.getElementById(type + 'Container');
            container.classList.add('focused');
        }

        function blurInput(type) {
            const container = document.getElementById(type + 'Container');
            container.classList.remove('focused');
        }

        function updateEfficiencyMeter(inputTokens, outputTokens, inputChars, outputChars) {
            const totalTokens = inputTokens + outputTokens;
            const totalChars = inputChars + outputChars;
            const efficiency = totalChars > 0 ? Math.min(100, (totalChars / (totalTokens * 4)) * 100) : 0;

            const efficiencyFill = document.getElementById('efficiencyFill');
            const efficiencyText = document.getElementById('efficiencyText');
            const efficiencyTips = document.getElementById('efficiencyTips');

            efficiencyFill.style.width = efficiency + '%';
            efficiencyText.textContent = Math.round(efficiency) + '%';

            let tips = '';
            if (efficiency > 80) {
                tips = '🎉 Excellent token efficiency! Your text is well-optimized.';
            } else if (efficiency > 60) {
                tips = '👍 Good efficiency. Consider removing unnecessary words or punctuation.';
            } else if (efficiency > 40) {
                tips = '⚠️ Moderate efficiency. Try shortening sentences and using simpler words.';
            } else {
                tips = '🔧 Low efficiency. Significant optimization possible - remove filler words, use abbreviations.';
            }

            efficiencyTips.textContent = tips;
            document.getElementById('efficiencyMeter').style.display = 'block';
        }

        function loadTemplate() {
            const templates = [
                {
                    name: "Positive Review Response",
                    input: "You are a customer service AI for CPR cell phone repair. Respond professionally to this positive review: 'Amazing service! They fixed my cracked screen in 30 minutes. Staff was friendly and prices were fair. Definitely coming back!'",
                    output: "Thank you for the wonderful review! We're thrilled you had a great experience with our screen repair service. Our team takes pride in quick, quality repairs at fair prices. We look forward to helping you with any future device needs!"
                },
                {
                    name: "Negative Review Response",
                    input: "You are a customer service AI for CPR cell phone repair. Respond professionally to this negative review: 'Terrible experience. Waited 2 hours, expensive prices, and my phone still has issues. Would not recommend.'",
                    output: "We sincerely apologize for your disappointing experience. This doesn't meet our service standards. Please contact our manager at [phone] so we can resolve the remaining issues and discuss your concerns about wait time and pricing. We value your feedback and want to make this right."
                },
                {
                    name: "Neutral Review Response",
                    input: "You are a customer service AI for CPR cell phone repair. Respond to this neutral review: 'Got my phone fixed here. Service was okay, took about an hour. Price was average for the area.'",
                    output: "Thank you for choosing CPR for your phone repair. We appreciate your feedback about our service and timing. We're always working to improve our customer experience. If you have any suggestions or future repair needs, please don't hesitate to reach out!"
                }
            ];

            const randomTemplate = templates[Math.floor(Math.random() * templates.length)];
            document.getElementById('inputText').value = randomTemplate.input;
            document.getElementById('outputText').value = randomTemplate.output;
            updateTokenCounts();

            // Show a notification
            showNotification(`📋 Loaded: ${randomTemplate.name}`, 'success');
        }

        function optimizeText() {
            const inputText = document.getElementById('inputText').value;
            const outputText = document.getElementById('outputText').value;

            // Simple optimization suggestions
            let optimizedInput = inputText
                .replace(/\s+/g, ' ')
                .replace(/\. /g, '. ')
                .replace(/very /gi, '')
                .replace(/really /gi, '')
                .replace(/quite /gi, '');

            let optimizedOutput = outputText
                .replace(/\s+/g, ' ')
                .replace(/\. /g, '. ')
                .replace(/very /gi, '')
                .replace(/really /gi, '')
                .replace(/quite /gi, '');

            document.getElementById('inputText').value = optimizedInput.trim();
            document.getElementById('outputText').value = optimizedOutput.trim();
            updateTokenCounts();

            showNotification('🎯 Text optimized! Removed filler words and extra spaces.', 'success');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        function animateValue(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const startTime = performance.now();

            function updateValue(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(start + (end - start) * progress);

                element.textContent = current;

                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                }
            }

            requestAnimationFrame(updateValue);
        }

        function animateCurrency(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const startTime = performance.now();

            function updateValue(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = start + (end - start) * progress;

                element.textContent = formatCurrency(current);

                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                }
            }

            requestAnimationFrame(updateValue);
        }

        let simulatorChart = null;

        function createSimulatorChart(providerData) {
            const ctx = document.getElementById('simulatorChart').getContext('2d');

            if (simulatorChart) {
                simulatorChart.destroy();
            }

            const labels = providerData.map(p => `${p.provider.name}\n${p.provider.model}`);
            const costs = providerData.map(p => p.costPerResponse * 1000000); // Convert to cost per million tokens
            const colors = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#6b7280'];

            simulatorChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Cost per Million Tokens ($)',
                        data: costs,
                        backgroundColor: colors,
                        borderColor: colors.map(c => c + '80'),
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '💰 Cost Comparison - Top 6 Providers',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Cost per Million Tokens ($)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toFixed(2);
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Providers'
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutBounce'
                    }
                }
            });
        }

        // Initialize with default calculation
        document.addEventListener('DOMContentLoaded', function() {
            calculateCosts();
            updateTokenCounts(); // Initialize token counts

            // Test the simulator with default values
            setTimeout(() => {
                simulateTextCost();
            }, 1000);
        });

        // Add event listeners for real-time updates
        ['numStores', 'responsesPerDay', 'inputTokens', 'outputTokens', 'primaryProvider', 'batchProcessing', 'contextCaching', 'analysisMode'].forEach(id => {
            document.getElementById(id).addEventListener('change', calculateCosts);
            document.getElementById(id).addEventListener('input', calculateCosts);
        });
    </script>
</body>
</html>